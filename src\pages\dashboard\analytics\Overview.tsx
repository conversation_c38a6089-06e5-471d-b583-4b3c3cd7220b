import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DollarSign,
  ShoppingCart,
  Users,
  TrendingUp,
  TrendingDown,
  Package,
  Eye,
  ArrowUpRight,
  Calendar,
  BarChart3,
} from "lucide-react";

// Mock analytics data
const metrics = [
  {
    title: "Total Revenue",
    value: "$45,231.89",
    change: "+20.1%",
    changeType: "positive" as const,
    icon: DollarSign,
    description: "from last month",
  },
  {
    title: "Orders",
    value: "2,350",
    change: "+180.1%",
    changeType: "positive" as const,
    icon: ShoppingCart,
    description: "from last month",
  },
  {
    title: "Customers",
    value: "573",
    change: "+201",
    changeType: "positive" as const,
    icon: Users,
    description: "new customers",
  },
  {
    title: "Conversion Rate",
    value: "3.2%",
    change: "-0.4%",
    changeType: "negative" as const,
    icon: TrendingUp,
    description: "from last month",
  },
];

const topProducts = [
  {
    id: "1",
    name: "Wireless Bluetooth Headphones",
    sales: 156,
    revenue: 15599.44,
    growth: 12.5,
  },
  {
    id: "2",
    name: "Organic Cotton T-Shirt",
    sales: 89,
    revenue: 2669.11,
    growth: 8.2,
  },
  {
    id: "3",
    name: "Yoga Mat Premium",
    sales: 67,
    revenue: 3349.33,
    growth: -2.1,
  },
  {
    id: "4",
    name: "Stainless Steel Water Bottle",
    sales: 45,
    revenue: 1124.55,
    growth: 15.8,
  },
];

const topCustomers = [
  {
    id: "1",
    name: "Olivia Martin",
    email: "<EMAIL>",
    orders: 12,
    spent: 1234.56,
  },
  {
    id: "2",
    name: "Jackson Lee",
    email: "<EMAIL>",
    orders: 8,
    spent: 892.34,
  },
  {
    id: "3",
    name: "Isabella Nguyen",
    email: "<EMAIL>",
    orders: 15,
    spent: 2156.78,
  },
];

const trafficSources = [
  { source: "Direct", visitors: 4234, percentage: 45.2 },
  { source: "Google", visitors: 2891, percentage: 30.8 },
  { source: "Social Media", visitors: 1456, percentage: 15.5 },
  { source: "Email", visitors: 789, percentage: 8.4 },
];

export default function AnalyticsOverview() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Analytics</h2>
          <p className="text-muted-foreground">
            Track your store's performance and insights
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select defaultValue="30days">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="1year">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          return (
            <Card key={metric.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                <p className="text-xs text-muted-foreground">
                  <span
                    className={
                      metric.changeType === "positive" ? "text-green-600" : "text-red-600"
                    }
                  >
                    {metric.changeType === "positive" ? (
                      <TrendingUp className="inline h-3 w-3 mr-1" />
                    ) : (
                      <TrendingDown className="inline h-3 w-3 mr-1" />
                    )}
                    {metric.change}
                  </span>{" "}
                  {metric.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Sales Chart Placeholder */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Revenue Overview</CardTitle>
            <CardDescription>
              Monthly revenue trends for the past 6 months
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
              <div className="text-center space-y-2">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto" />
                <p className="text-muted-foreground">Revenue Chart</p>
                <p className="text-sm text-muted-foreground">
                  Chart component would be integrated here
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Traffic Sources */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Traffic Sources</CardTitle>
            <CardDescription>Where your visitors are coming from</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {trafficSources.map((source) => (
                <div key={source.source} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">{source.source}</span>
                    <span className="text-muted-foreground">
                      {source.visitors.toLocaleString()} ({source.percentage}%)
                    </span>
                  </div>
                  <Progress value={source.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {/* Top Products */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Top Products</CardTitle>
              <CardDescription>Best performing products this month</CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link to="/dashboard/analytics/sales">
                <Eye className="mr-2 h-4 w-4" />
                View All
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div key={product.id} className="flex items-center space-x-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted text-sm font-medium">
                    {index + 1}
                  </div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium leading-none">{product.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {product.sales} sales • ${product.revenue.toFixed(2)}
                    </p>
                  </div>
                  <Badge
                    variant={product.growth > 0 ? "default" : "destructive"}
                    className="text-xs"
                  >
                    {product.growth > 0 ? "+" : ""}{product.growth}%
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Customers */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Top Customers</CardTitle>
              <CardDescription>Highest value customers this month</CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link to="/dashboard/analytics/customers">
                <Eye className="mr-2 h-4 w-4" />
                View All
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topCustomers.map((customer, index) => (
                <div key={customer.id} className="flex items-center space-x-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted text-sm font-medium">
                    {index + 1}
                  </div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium leading-none">{customer.name}</p>
                    <p className="text-sm text-muted-foreground">{customer.email}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">${customer.spent.toFixed(2)}</p>
                    <p className="text-xs text-muted-foreground">{customer.orders} orders</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Insights</CardTitle>
          <CardDescription>
            Key performance indicators and actionable insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Package className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Inventory Alert</span>
              </div>
              <p className="text-sm text-muted-foreground">
                3 products are running low on stock
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link to="/dashboard/products">
                  View Products
                  <ArrowUpRight className="ml-2 h-3 w-3" />
                </Link>
              </Button>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Peak Hours</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Most sales happen between 2-4 PM
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link to="/dashboard/analytics/sales">
                  View Details
                  <ArrowUpRight className="ml-2 h-3 w-3" />
                </Link>
              </Button>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium">Customer Retention</span>
              </div>
              <p className="text-sm text-muted-foreground">
                68% of customers make repeat purchases
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link to="/dashboard/analytics/customers">
                  View Analysis
                  <ArrowUpRight className="ml-2 h-3 w-3" />
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
