import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, CreditCard } from "lucide-react";

export default function SettingsPayments() {
  const navigate = useNavigate();

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="icon" onClick={() => navigate("/dashboard/settings")}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Payment Settings</h2>
          <p className="text-muted-foreground">Configure payment methods and gateways</p>
        </div>
      </div>
      <div className="h-[400px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
        <div className="text-center space-y-2">
          <CreditCard className="h-16 w-16 text-muted-foreground mx-auto" />
          <p className="text-lg font-medium text-muted-foreground">Payment Settings</p>
          <p className="text-sm text-muted-foreground">Payment configuration would be implemented here</p>
        </div>
      </div>
    </div>
  );
}
