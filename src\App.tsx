import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";
import DashboardLayout from "./layouts/DashboardLayout";
import AccountSettings from "./pages/dashboard/account/Settings";
import AnalyticsCustomers from "./pages/dashboard/analytics/Customers";
import AnalyticsOverview from "./pages/dashboard/analytics/Overview";
import AnalyticsSales from "./pages/dashboard/analytics/Sales";
import AppAdd from "./pages/dashboard/apps/Add";
import AppList from "./pages/dashboard/apps/List";
import CustomerAdd from "./pages/dashboard/customers/Add";
import CustomerList from "./pages/dashboard/customers/List";
import CustomerProfile from "./pages/dashboard/customers/Profile";
import DiscountAdd from "./pages/dashboard/discounts/Add";
import DiscountEdit from "./pages/dashboard/discounts/Edit";
import DiscountList from "./pages/dashboard/discounts/List";
import DashboardHome from "./pages/dashboard/Home";
import OrderDetails from "./pages/dashboard/orders/Details";
import OrderList from "./pages/dashboard/orders/List";
import POSHome from "./pages/dashboard/pos/Home";
import POSOrders from "./pages/dashboard/pos/Orders";
import ProductAdd from "./pages/dashboard/products/Add";
import ProductDuplicate from "./pages/dashboard/products/Duplicate";
import ProductEdit from "./pages/dashboard/products/Edit";
import ProductList from "./pages/dashboard/products/List";
import SettingsGeneral from "./pages/dashboard/settings/General";
import SettingsHome from "./pages/dashboard/settings/Home";
import SettingsNotifications from "./pages/dashboard/settings/Notifications";
import SettingsPayments from "./pages/dashboard/settings/Payments";
import SettingsShipping from "./pages/dashboard/settings/Shipping";
import SettingsTaxes from "./pages/dashboard/settings/Taxes";

const App = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Redirect root to dashboard */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />

        {/* Dashboard routes */}
        <Route path="/dashboard" element={<DashboardLayout />}>
          <Route index element={<DashboardHome />} />

          {/* Products */}
          <Route path="products" element={<ProductList />} />
          <Route path="products/add" element={<ProductAdd />} />
          <Route path="products/:id" element={<ProductEdit />} />
          <Route path="products/:id/duplicate" element={<ProductDuplicate />} />

          {/* Orders */}
          <Route path="orders" element={<OrderList />} />
          <Route path="orders/:id" element={<OrderDetails />} />

          {/* Customers */}
          <Route path="customers" element={<CustomerList />} />
          <Route path="customers/add" element={<CustomerAdd />} />
          <Route path="customers/:id" element={<CustomerProfile />} />

          {/* POS */}
          <Route path="pos" element={<POSHome />} />
          <Route path="pos/orders" element={<POSOrders />} />

          {/* Analytics */}
          <Route path="analytics" element={<AnalyticsOverview />} />
          <Route path="analytics/sales" element={<AnalyticsSales />} />
          <Route path="analytics/customers" element={<AnalyticsCustomers />} />

          {/* Discounts */}
          <Route path="discounts" element={<DiscountList />} />
          <Route path="discounts/add" element={<DiscountAdd />} />
          <Route path="discounts/:id" element={<DiscountEdit />} />

          {/* Apps */}
          <Route path="apps" element={<AppList />} />
          <Route path="apps/add" element={<AppAdd />} />

          {/* Settings */}
          <Route path="settings" element={<SettingsHome />} />
          <Route path="settings/general" element={<SettingsGeneral />} />
          <Route path="settings/payments" element={<SettingsPayments />} />
          <Route path="settings/shipping" element={<SettingsShipping />} />
          <Route path="settings/taxes" element={<SettingsTaxes />} />
          <Route
            path="settings/notifications"
            element={<SettingsNotifications />}
          />

          {/* Account */}
          <Route path="account" element={<AccountSettings />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
};

export default App;
