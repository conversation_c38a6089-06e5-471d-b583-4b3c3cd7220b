import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Plus,
  Search,
  Puzzle,
  Star,
  Download,
  Settings,
  ExternalLink,
} from "lucide-react";

// Mock apps data
const installedApps = [
  {
    id: "1",
    name: "Email Marketing Pro",
    description: "Advanced email marketing automation and campaigns",
    icon: "/placeholder-app.jpg",
    version: "2.1.0",
    rating: 4.8,
    status: "active",
    category: "Marketing",
  },
  {
    id: "2",
    name: "Inventory Sync",
    description: "Real-time inventory synchronization across platforms",
    icon: "/placeholder-app.jpg",
    version: "1.5.2",
    rating: 4.6,
    status: "active",
    category: "Inventory",
  },
  {
    id: "3",
    name: "Analytics Dashboard",
    description: "Advanced analytics and reporting tools",
    icon: "/placeholder-app.jpg",
    version: "3.0.1",
    rating: 4.9,
    status: "inactive",
    category: "Analytics",
  },
];

const availableApps = [
  {
    id: "4",
    name: "Social Media Manager",
    description: "Manage all your social media accounts from one place",
    icon: "/placeholder-app.jpg",
    rating: 4.7,
    downloads: "10k+",
    price: "Free",
    category: "Marketing",
  },
  {
    id: "5",
    name: "Customer Reviews",
    description: "Collect and display customer reviews and ratings",
    icon: "/placeholder-app.jpg",
    rating: 4.5,
    downloads: "5k+",
    price: "$9.99/mo",
    category: "Customer Service",
  },
  {
    id: "6",
    name: "Shipping Calculator",
    description: "Real-time shipping rates from multiple carriers",
    icon: "/placeholder-app.jpg",
    rating: 4.4,
    downloads: "8k+",
    price: "$14.99/mo",
    category: "Shipping",
  },
];

export default function AppList() {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("installed");

  const filteredInstalledApps = installedApps.filter((app) =>
    app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredAvailableApps = availableApps.filter((app) =>
    app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="default">Active</Badge>;
      case "inactive":
        return <Badge variant="secondary">Inactive</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-3 w-3 ${
          i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Apps</h2>
          <p className="text-muted-foreground">
            Extend your store's functionality with apps
          </p>
        </div>
        <Button asChild>
          <Link to="/dashboard/apps/add">
            <Plus className="mr-2 h-4 w-4" />
            Browse Apps
          </Link>
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Installed Apps</CardTitle>
            <Puzzle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{installedApps.length}</div>
            <p className="text-xs text-muted-foreground">
              {installedApps.filter(app => app.status === "active").length} active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">6</div>
            <p className="text-xs text-muted-foreground">
              App categories
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Updates Available</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">
              Apps need updates
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Cost</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$24.98</div>
            <p className="text-xs text-muted-foreground">
              App subscriptions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search apps..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
        <Button
          variant={activeTab === "installed" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("installed")}
        >
          Installed Apps
        </Button>
        <Button
          variant={activeTab === "available" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("available")}
        >
          App Store
        </Button>
      </div>

      {/* Installed Apps */}
      {activeTab === "installed" && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredInstalledApps.map((app) => (
            <Card key={app.id}>
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={app.icon} alt={app.name} />
                    <AvatarFallback>
                      <Puzzle className="h-6 w-6" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{app.name}</CardTitle>
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        {renderStars(app.rating)}
                        <span className="text-sm text-muted-foreground">
                          {app.rating}
                        </span>
                      </div>
                      {getStatusBadge(app.status)}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  {app.description}
                </p>
                <div className="flex items-center justify-between">
                  <div className="text-sm">
                    <span className="text-muted-foreground">Version:</span> {app.version}
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Available Apps */}
      {activeTab === "available" && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredAvailableApps.map((app) => (
            <Card key={app.id}>
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={app.icon} alt={app.name} />
                    <AvatarFallback>
                      <Puzzle className="h-6 w-6" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{app.name}</CardTitle>
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        {renderStars(app.rating)}
                        <span className="text-sm text-muted-foreground">
                          {app.rating}
                        </span>
                      </div>
                      <Badge variant="outline">{app.category}</Badge>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  {app.description}
                </p>
                <div className="flex items-center justify-between">
                  <div className="text-sm">
                    <div className="font-medium">{app.price}</div>
                    <div className="text-muted-foreground">{app.downloads} downloads</div>
                  </div>
                  <Button size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    Install
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
