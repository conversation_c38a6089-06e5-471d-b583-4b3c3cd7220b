import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Copy,
  Edit,
  Eye,
  MoreHorizontal,
  Package,
  Plus,
  Search,
  Trash2,
} from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";

// Enhanced Mock data with comprehensive product information
const products = [
  {
    id: "1",
    name: "Wireless Bluetooth Headphones Pro",
    sku: "WBH-001",
    barcode: "123456789012",
    price: 99.99,
    compareAtPrice: 129.99,
    costPerItem: 45.0,
    inventory: 45,
    status: "Active",
    category: "Electronics",
    vendor: "TechSound",
    productType: "Headphones",
    tags: ["wireless", "bluetooth", "premium", "noise-cancelling"],
    image: "/placeholder-product.jpg",
    images: ["/placeholder-product.jpg", "/placeholder-product-2.jpg"],
    weight: 0.5,
    hasVariants: true,
    variantCount: 3,
    salesLast30Days: 156,
    salesRevenue: 15599.44,
    salesGrowth: 12.5,
    publishedChannels: ["Online Store", "POS", "Instagram"],
    seoTitle: "Premium Wireless Bluetooth Headphones - TechSound Pro",
    seoDescription:
      "Experience premium sound quality with our wireless bluetooth headphones",
    createdAt: "2024-01-01",
    updatedAt: "2024-01-15",
  },
  {
    id: "2",
    name: "Organic Cotton T-Shirt",
    sku: "OCT-002",
    barcode: "234567890123",
    price: 29.99,
    compareAtPrice: 39.99,
    costPerItem: 12.0,
    inventory: 120,
    status: "Active",
    category: "Clothing",
    vendor: "EcoWear",
    productType: "T-Shirts",
    tags: ["organic", "cotton", "sustainable", "unisex"],
    image: "/placeholder-product.jpg",
    images: ["/placeholder-product.jpg"],
    weight: 0.2,
    hasVariants: true,
    variantCount: 12,
    salesLast30Days: 89,
    salesRevenue: 2669.11,
    salesGrowth: 8.2,
    publishedChannels: ["Online Store", "POS", "Facebook"],
    seoTitle: "Organic Cotton T-Shirt - Sustainable Fashion",
    seoDescription:
      "Comfortable organic cotton t-shirt made from sustainable materials",
    createdAt: "2024-01-02",
    updatedAt: "2024-01-14",
  },
  {
    id: "3",
    name: "Stainless Steel Water Bottle",
    sku: "SSW-003",
    barcode: "345678901234",
    price: 24.99,
    compareAtPrice: null,
    costPerItem: 8.5,
    inventory: 0,
    status: "Out of Stock",
    category: "Accessories",
    vendor: "HydroLife",
    productType: "Water Bottles",
    tags: ["stainless-steel", "insulated", "eco-friendly"],
    image: "/placeholder-product.jpg",
    images: ["/placeholder-product.jpg"],
    weight: 0.4,
    hasVariants: true,
    variantCount: 4,
    salesLast30Days: 45,
    salesRevenue: 1124.55,
    salesGrowth: 15.8,
    publishedChannels: ["Online Store", "POS"],
    seoTitle: "Insulated Stainless Steel Water Bottle - 24oz",
    seoDescription:
      "Keep your drinks at the perfect temperature with our insulated water bottle",
    createdAt: "2024-01-03",
    updatedAt: "2024-01-13",
  },
  {
    id: "4",
    name: "Yoga Mat Premium",
    sku: "YMP-004",
    barcode: "456789012345",
    price: 49.99,
    compareAtPrice: 69.99,
    costPerItem: 18.0,
    inventory: 30,
    status: "Active",
    category: "Sports",
    vendor: "ZenFit",
    productType: "Yoga Equipment",
    tags: ["yoga", "premium", "non-slip", "eco-friendly"],
    image: "/placeholder-product.jpg",
    images: ["/placeholder-product.jpg"],
    weight: 1.2,
    hasVariants: true,
    variantCount: 6,
    salesLast30Days: 67,
    salesRevenue: 3349.33,
    salesGrowth: -2.1,
    publishedChannels: ["Online Store", "POS", "Instagram"],
    seoTitle: "Premium Non-Slip Yoga Mat - ZenFit Pro",
    seoDescription:
      "Professional-grade yoga mat with superior grip and comfort",
    createdAt: "2024-01-04",
    updatedAt: "2024-01-12",
  },
  {
    id: "5",
    name: "Coffee Mug Set",
    sku: "CMS-005",
    barcode: "567890123456",
    price: 19.99,
    compareAtPrice: null,
    costPerItem: 6.5,
    inventory: 75,
    status: "Draft",
    category: "Home",
    vendor: "BrewMaster",
    productType: "Drinkware",
    tags: ["coffee", "ceramic", "set", "gift"],
    image: "/placeholder-product.jpg",
    images: ["/placeholder-product.jpg"],
    weight: 0.8,
    hasVariants: false,
    variantCount: 0,
    salesLast30Days: 0,
    salesRevenue: 0,
    salesGrowth: 0,
    publishedChannels: [],
    seoTitle: "Premium Ceramic Coffee Mug Set",
    seoDescription:
      "Beautiful ceramic coffee mug set perfect for daily use or gifts",
    createdAt: "2024-01-05",
    updatedAt: "2024-01-11",
  },
];

export default function ProductList() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [vendorFilter, setVendorFilter] = useState("all");
  const [inventoryFilter, setInventoryFilter] = useState("all");
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [showBulkActions, setShowBulkActions] = useState(false);

  const filteredProducts = products.filter((product) => {
    const matchesSearch =
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.barcode.includes(searchTerm) ||
      product.tags.some((tag) =>
        tag.toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesStatus =
      statusFilter === "all" ||
      product.status.toLowerCase() === statusFilter.toLowerCase();
    const matchesCategory =
      categoryFilter === "all" ||
      product.category.toLowerCase() === categoryFilter.toLowerCase();
    const matchesVendor =
      vendorFilter === "all" ||
      product.vendor.toLowerCase() === vendorFilter.toLowerCase();

    let matchesInventory = true;
    if (inventoryFilter === "low")
      matchesInventory = product.inventory > 0 && product.inventory <= 10;
    else if (inventoryFilter === "out")
      matchesInventory = product.inventory === 0;
    else if (inventoryFilter === "in-stock")
      matchesInventory = product.inventory > 10;

    return (
      matchesSearch &&
      matchesStatus &&
      matchesCategory &&
      matchesVendor &&
      matchesInventory
    );
  });

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    let aValue: any = a[sortBy as keyof typeof a];
    let bValue: any = b[sortBy as keyof typeof b];

    if (
      sortBy === "salesRevenue" ||
      sortBy === "salesLast30Days" ||
      sortBy === "price"
    ) {
      aValue = Number(aValue);
      bValue = Number(bValue);
    }

    if (sortOrder === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(sortedProducts.map((p) => p.id));
    } else {
      setSelectedProducts([]);
    }
  };

  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId]);
    } else {
      setSelectedProducts(selectedProducts.filter((id) => id !== productId));
    }
  };

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action: ${action} on products:`, selectedProducts);
    // Implement bulk actions here
    setSelectedProducts([]);
    setShowBulkActions(false);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge variant="default">Active</Badge>;
      case "Draft":
        return <Badge variant="secondary">Draft</Badge>;
      case "Out of Stock":
        return <Badge variant="destructive">Out of Stock</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Products</h2>
          <p className="text-muted-foreground">
            Manage your product inventory and listings
          </p>
        </div>
        <Button asChild>
          <Link to="/dashboard/products/add">
            <Plus className="mr-2 h-4 w-4" />
            Add Product
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Product Inventory</CardTitle>
          <CardDescription>
            A list of all products in your store including their name, status,
            price, and inventory.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center space-x-4 mb-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="out of stock">Out of Stock</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="electronics">Electronics</SelectItem>
                <SelectItem value="clothing">Clothing</SelectItem>
                <SelectItem value="accessories">Accessories</SelectItem>
                <SelectItem value="sports">Sports</SelectItem>
                <SelectItem value="home">Home</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Products Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Inventory</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center space-y-2">
                        <Package className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          No products found
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage
                              src={product.image}
                              alt={product.name}
                            />
                            <AvatarFallback>
                              <Package className="h-4 w-4" />
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{product.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {product.category}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {product.sku}
                      </TableCell>
                      <TableCell>{getStatusBadge(product.status)}</TableCell>
                      <TableCell>
                        <span
                          className={
                            product.inventory === 0 ? "text-red-600" : ""
                          }
                        >
                          {product.inventory} units
                        </span>
                      </TableCell>
                      <TableCell>${product.price.toFixed(2)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem asChild>
                              <Link to={`/dashboard/products/${product.id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link to={`/dashboard/products/${product.id}`}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link
                                to={`/dashboard/products/${product.id}/duplicate`}
                              >
                                <Copy className="mr-2 h-4 w-4" />
                                Duplicate
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
