import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, Bell } from "lucide-react";

export default function SettingsNotifications() {
  const navigate = useNavigate();

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="icon" onClick={() => navigate("/dashboard/settings")}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Notification Settings</h2>
          <p className="text-muted-foreground">Configure email templates and notifications</p>
        </div>
      </div>
      <div className="h-[400px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
        <div className="text-center space-y-2">
          <Bell className="h-16 w-16 text-muted-foreground mx-auto" />
          <p className="text-lg font-medium text-muted-foreground">Notification Settings</p>
          <p className="text-sm text-muted-foreground">Notification configuration would be implemented here</p>
        </div>
      </div>
    </div>
  );
}
