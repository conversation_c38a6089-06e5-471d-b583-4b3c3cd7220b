import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Users } from "lucide-react";
import { useNavigate } from "react-router-dom";

export default function AnalyticsCustomers() {
  const navigate = useNavigate();

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="icon" onClick={() => navigate("/dashboard/analytics")}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Customer Analytics</h2>
          <p className="text-muted-foreground">Customer insights and behavior analysis</p>
        </div>
      </div>

      <div className="h-[400px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
        <div className="text-center space-y-2">
          <Users className="h-16 w-16 text-muted-foreground mx-auto" />
          <p className="text-lg font-medium text-muted-foreground">Customer Analytics</p>
          <p className="text-sm text-muted-foreground">
            Customer behavior and insights charts would be implemented here
          </p>
        </div>
      </div>
    </div>
  );
}
