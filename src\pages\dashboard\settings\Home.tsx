import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Settings,
  Store,
  CreditCard,
  Truck,
  Calculator,
  Bell,
  Shield,
  Users,
  ArrowRight,
} from "lucide-react";

const settingsCategories = [
  {
    title: "General",
    description: "Store information, business details, and basic settings",
    icon: Store,
    href: "/dashboard/settings/general",
    items: ["Store name", "Business address", "Contact information", "Time zone"],
  },
  {
    title: "Payments",
    description: "Payment methods, gateways, and transaction settings",
    icon: CreditCard,
    href: "/dashboard/settings/payments",
    items: ["Payment gateways", "Currency settings", "Tax calculations", "Refund policies"],
  },
  {
    title: "Shipping",
    description: "Shipping zones, rates, and delivery options",
    icon: Truck,
    href: "/dashboard/settings/shipping",
    items: ["Shipping zones", "Delivery rates", "Carrier integrations", "Packaging"],
  },
  {
    title: "Taxes",
    description: "Tax rates, exemptions, and compliance settings",
    icon: Calculator,
    href: "/dashboard/settings/taxes",
    items: ["Tax rates", "Tax exemptions", "Regional settings", "Reporting"],
  },
  {
    title: "Notifications",
    description: "Email templates, alerts, and communication preferences",
    icon: Bell,
    href: "/dashboard/settings/notifications",
    items: ["Email templates", "Order notifications", "Customer alerts", "Admin notifications"],
  },
  {
    title: "Security",
    description: "User permissions, authentication, and security policies",
    icon: Shield,
    href: "/dashboard/settings/security",
    items: ["User roles", "Two-factor auth", "API keys", "Security logs"],
  },
];

export default function SettingsHome() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
          <p className="text-muted-foreground">
            Configure your store settings and preferences
          </p>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Store Status</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Online</div>
            <p className="text-xs text-muted-foreground">
              Store is live and accepting orders
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Payment Methods</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">
              Active payment gateways
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shipping Zones</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5</div>
            <p className="text-xs text-muted-foreground">
              Configured shipping zones
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">
              Active team members
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Settings Categories */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {settingsCategories.map((category) => {
          const Icon = category.icon;
          return (
            <Card key={category.title} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                    <Icon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{category.title}</CardTitle>
                  </div>
                </div>
                <CardDescription>{category.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-muted-foreground mb-4">
                  {category.items.map((item) => (
                    <li key={item} className="flex items-center">
                      <span className="w-1 h-1 bg-muted-foreground rounded-full mr-2" />
                      {item}
                    </li>
                  ))}
                </ul>
                <Button variant="outline" className="w-full" asChild>
                  <Link to={category.href}>
                    Configure
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common settings and configurations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Button variant="outline" className="h-auto p-4 flex-col space-y-2" asChild>
              <Link to="/dashboard/settings/general">
                <Store className="h-6 w-6" />
                <span>Store Info</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex-col space-y-2" asChild>
              <Link to="/dashboard/settings/payments">
                <CreditCard className="h-6 w-6" />
                <span>Payment Setup</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex-col space-y-2" asChild>
              <Link to="/dashboard/settings/shipping">
                <Truck className="h-6 w-6" />
                <span>Shipping Rates</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex-col space-y-2" asChild>
              <Link to="/dashboard/settings/taxes">
                <Calculator className="h-6 w-6" />
                <span>Tax Settings</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
