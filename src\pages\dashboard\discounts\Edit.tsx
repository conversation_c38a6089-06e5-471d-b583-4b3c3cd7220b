import { useNavigate, useParams } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function DiscountEdit() {
  const navigate = useNavigate();
  const { id } = useParams();

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="icon" onClick={() => navigate("/dashboard/discounts")}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Edit Discount</h2>
          <p className="text-muted-foreground">Modify discount settings</p>
        </div>
      </div>
      <div className="h-[400px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
        <p className="text-muted-foreground">Edit discount form would be implemented here</p>
      </div>
    </div>
  );
}
