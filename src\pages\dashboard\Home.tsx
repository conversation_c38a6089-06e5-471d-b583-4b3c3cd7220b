import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  ArrowUpRight,
  DollarSign,
  Package,
  ShoppingCart,
  Users,
  TrendingUp,
  Plus,
  Eye,
  CreditCard,
  Tag,
} from "lucide-react";

const metrics = [
  {
    title: "Total Revenue",
    value: "$45,231.89",
    change: "+20.1%",
    changeType: "positive" as const,
    icon: DollarSign,
  },
  {
    title: "Orders",
    value: "2,350",
    change: "+180.1%",
    changeType: "positive" as const,
    icon: ShoppingCart,
  },
  {
    title: "Products",
    value: "12,234",
    change: "+19%",
    changeType: "positive" as const,
    icon: Package,
  },
  {
    title: "Active Customers",
    value: "573",
    change: "+201",
    changeType: "positive" as const,
    icon: Users,
  },
];

const quickActions = [
  {
    title: "Add Product",
    description: "Create a new product for your store",
    href: "/dashboard/products/add",
    icon: Package,
    color: "bg-blue-500",
  },
  {
    title: "Create Discount",
    description: "Set up a new discount or promotion",
    href: "/dashboard/discounts/add",
    icon: Tag,
    color: "bg-green-500",
  },
  {
    title: "Open POS",
    description: "Start selling with point of sale",
    href: "/dashboard/pos",
    icon: CreditCard,
    color: "bg-purple-500",
  },
  {
    title: "View Analytics",
    description: "Check your store performance",
    href: "/dashboard/analytics",
    icon: TrendingUp,
    color: "bg-orange-500",
  },
];

const recentOrders = [
  {
    id: "#3210",
    customer: "Olivia Martin",
    email: "<EMAIL>",
    amount: "$42.25",
    status: "Paid",
    avatar: "/placeholder-avatar.jpg",
  },
  {
    id: "#3209",
    customer: "Jackson Lee",
    email: "<EMAIL>",
    amount: "$74.99",
    status: "Pending",
    avatar: "/placeholder-avatar.jpg",
  },
  {
    id: "#3208",
    customer: "Isabella Nguyen",
    email: "<EMAIL>",
    amount: "$99.99",
    status: "Paid",
    avatar: "/placeholder-avatar.jpg",
  },
  {
    id: "#3207",
    customer: "William Kim",
    email: "<EMAIL>",
    amount: "$39.99",
    status: "Paid",
    avatar: "/placeholder-avatar.jpg",
  },
];

export default function DashboardHome() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <div className="flex items-center space-x-2">
          <Button asChild>
            <Link to="/dashboard/products/add">
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Link>
          </Button>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          return (
            <Card key={metric.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                <p className="text-xs text-muted-foreground">
                  <span
                    className={
                      metric.changeType === "positive" ? "text-green-600" : "text-red-600"
                    }
                  >
                    {metric.change}
                  </span>{" "}
                  from last month
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Quick Actions */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Frequently used actions to manage your store</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4 md:grid-cols-2">
            {quickActions.map((action) => {
              const Icon = action.icon;
              return (
                <Link
                  key={action.title}
                  to={action.href}
                  className="flex items-center space-x-4 rounded-lg border p-4 transition-colors hover:bg-accent"
                >
                  <div className={`rounded-lg p-2 text-white ${action.color}`}>
                    <Icon className="h-6 w-6" />
                  </div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium leading-none">{action.title}</p>
                    <p className="text-sm text-muted-foreground">{action.description}</p>
                  </div>
                  <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
                </Link>
              );
            })}
          </CardContent>
        </Card>

        {/* Recent Orders */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
            <CardDescription>Latest orders from your customers</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              {recentOrders.map((order) => (
                <div key={order.id} className="flex items-center">
                  <Avatar className="h-9 w-9">
                    <AvatarImage src={order.avatar} alt="Avatar" />
                    <AvatarFallback>
                      {order.customer
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="ml-4 space-y-1">
                    <p className="text-sm font-medium leading-none">{order.customer}</p>
                    <p className="text-sm text-muted-foreground">{order.email}</p>
                  </div>
                  <div className="ml-auto flex items-center space-x-2">
                    <Badge variant={order.status === "Paid" ? "default" : "secondary"}>
                      {order.status}
                    </Badge>
                    <div className="font-medium">{order.amount}</div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full" asChild>
                <Link to="/dashboard/orders">
                  <Eye className="mr-2 h-4 w-4" />
                  View All Orders
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
