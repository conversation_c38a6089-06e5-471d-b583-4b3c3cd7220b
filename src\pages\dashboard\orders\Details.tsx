import { useState } from "react";
import { useNavi<PERSON>, usePara<PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  ArrowLeft,
  MoreHorizontal,
  Package,
  Truck,
  DollarSign,
  MapPin,
  Phone,
  Mail,
  Calendar,
  CreditCard,
  FileText,
  Edit,
} from "lucide-react";

// Mock order data
const mockOrder = {
  id: "3210",
  orderNumber: "#3210",
  date: "2024-01-15T10:30:00Z",
  customer: {
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    avatar: "/placeholder-avatar.jpg",
  },
  shippingAddress: {
    name: "Olivia Martin",
    address1: "123 Main Street",
    address2: "Apt 4B",
    city: "New York",
    state: "NY",
    zip: "10001",
    country: "United States",
  },
  billingAddress: {
    name: "Olivia Martin",
    address1: "123 Main Street",
    address2: "Apt 4B",
    city: "New York",
    state: "NY",
    zip: "10001",
    country: "United States",
  },
  items: [
    {
      id: "1",
      name: "Wireless Bluetooth Headphones",
      sku: "WBH-001",
      quantity: 1,
      price: 29.99,
      image: "/placeholder-product.jpg",
    },
    {
      id: "2",
      name: "USB-C Cable",
      sku: "USC-002",
      quantity: 1,
      price: 12.26,
      image: "/placeholder-product.jpg",
    },
  ],
  subtotal: 42.25,
  shipping: 0,
  tax: 0,
  total: 42.25,
  paymentStatus: "paid",
  fulfillmentStatus: "fulfilled",
  paymentMethod: "Credit Card ending in 4242",
  trackingNumber: "1Z999AA1234567890",
  notes: "Customer requested expedited shipping",
  timeline: [
    {
      id: "1",
      action: "Order placed",
      date: "2024-01-15T10:30:00Z",
      description: "Order was successfully placed by customer",
    },
    {
      id: "2",
      action: "Payment confirmed",
      date: "2024-01-15T10:31:00Z",
      description: "Payment of $42.25 was processed successfully",
    },
    {
      id: "3",
      action: "Order fulfilled",
      date: "2024-01-15T14:20:00Z",
      description: "Order was packed and shipped",
    },
    {
      id: "4",
      action: "Tracking updated",
      date: "2024-01-15T16:45:00Z",
      description: "Package is in transit",
    },
  ],
};

export default function OrderDetails() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [notes, setNotes] = useState(mockOrder.notes);

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case "paid":
        return <Badge variant="default">Paid</Badge>;
      case "pending":
        return <Badge variant="secondary">Pending</Badge>;
      case "refunded":
        return <Badge variant="outline">Refunded</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getFulfillmentStatusBadge = (status: string) => {
    switch (status) {
      case "fulfilled":
        return <Badge variant="default">Fulfilled</Badge>;
      case "partially_fulfilled":
        return <Badge variant="secondary">Partially Fulfilled</Badge>;
      case "unfulfilled":
        return <Badge variant="destructive">Unfulfilled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatAddress = (address: typeof mockOrder.shippingAddress) => {
    return [
      address.name,
      address.address1,
      address.address2,
      `${address.city}, ${address.state} ${address.zip}`,
      address.country,
    ]
      .filter(Boolean)
      .join("\n");
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" onClick={() => navigate("/dashboard/orders")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <h2 className="text-3xl font-bold tracking-tight">Order {mockOrder.orderNumber}</h2>
              {getPaymentStatusBadge(mockOrder.paymentStatus)}
              {getFulfillmentStatusBadge(mockOrder.fulfillmentStatus)}
            </div>
            <p className="text-muted-foreground">
              Placed on {formatDate(mockOrder.date)}
            </p>
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              <MoreHorizontal className="mr-2 h-4 w-4" />
              Actions
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Order Actions</DropdownMenuLabel>
            <DropdownMenuItem>
              <Package className="mr-2 h-4 w-4" />
              Fulfill Order
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Truck className="mr-2 h-4 w-4" />
              Track Shipment
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Edit className="mr-2 h-4 w-4" />
              Edit Order
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <DollarSign className="mr-2 h-4 w-4" />
              Refund Order
            </DropdownMenuItem>
            <DropdownMenuItem>
              <FileText className="mr-2 h-4 w-4" />
              Print Invoice
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle>Order Items</CardTitle>
              <CardDescription>
                Items included in this order
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead className="text-right">Price</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockOrder.items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={item.image} alt={item.name} />
                            <AvatarFallback>
                              <Package className="h-4 w-4" />
                            </AvatarFallback>
                          </Avatar>
                          <div className="font-medium">{item.name}</div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{item.sku}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell className="text-right">${item.price.toFixed(2)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <Separator className="my-4" />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>${mockOrder.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping</span>
                  <span>${mockOrder.shipping.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax</span>
                  <span>${mockOrder.tax.toFixed(2)}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span>${mockOrder.total.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Order Timeline</CardTitle>
              <CardDescription>
                Track the progress of this order
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockOrder.timeline.map((event, index) => (
                  <div key={event.id} className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm">
                        {index + 1}
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{event.action}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(event.date)}
                        </p>
                      </div>
                      <p className="text-sm text-muted-foreground">{event.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle>Customer</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={mockOrder.customer.avatar} alt={mockOrder.customer.name} />
                  <AvatarFallback>
                    {mockOrder.customer.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{mockOrder.customer.name}</div>
                  <div className="text-sm text-muted-foreground">Customer</div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{mockOrder.customer.email}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{mockOrder.customer.phone}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle>Shipping Address</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start space-x-2">
                <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                <div className="text-sm whitespace-pre-line">
                  {formatAddress(mockOrder.shippingAddress)}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Information */}
          <Card>
            <CardHeader>
              <CardTitle>Payment</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{mockOrder.paymentMethod}</span>
              </div>
              {mockOrder.trackingNumber && (
                <div>
                  <Label className="text-sm font-medium">Tracking Number</Label>
                  <p className="text-sm font-mono">{mockOrder.trackingNumber}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="notes">Order Notes</Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Add notes about this order..."
                  className="min-h-[100px]"
                />
                <Button size="sm" className="w-full">
                  Save Notes
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
